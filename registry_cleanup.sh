#!/bin/bash

GITLAB_URL="https://gitlab.imtins.com"
PRIVATE_TOKEN="**************************"
CUTOFF_DATE=$(date -d "2 years ago" +%s)



# Get all projects with container registry enabled
projects=$(curl -s --header "PRIVATE-TOKEN: $PRIVATE_TOKEN" \
  "$GITLAB_URL/api/v4/projects?per_page=100&container_registry_enabled=true" | jq -r '.[].id')

for project_id in $projects; do
  echo "Processing project: $project_id"
  
  # Get all repositories in the project
  repositories=$(curl -s --header "PRIVATE-TOKEN: $PRIVATE_TOKEN" \
    "$GITLAB_URL/api/v4/projects/$project_id/registry/repositories" | jq -r '.[].id')
  
  for repo_id in $repositories; do
    # Get all tags for this repository
    tags=$(curl -s --header "PRIVATE-TOKEN: $PRIVATE_TOKEN" \
      "$GITLAB_URL/api/v4/projects/$project_id/registry/repositories/$repo_id/tags?per_page=100")
    
    # Parse and delete old tags
    echo "$tags" | jq -c '.[]' | while read tag; do
      created_at=$(echo "$tag" | jq -r '.created_at')
      tag_name=$(echo "$tag" | jq -r '.name')
      
      # Convert created_at to timestamp
      tag_timestamp=$(date -d "$created_at" +%s)
      
      if [ "$tag_timestamp" -lt "$CUTOFF_DATE" ]; then
        echo "  Deleting old tag: $tag_name (created: $created_at)"
        # curl -s --request DELETE --header "PRIVATE-TOKEN: $PRIVATE_TOKEN" \
        #   "$GITLAB_URL/api/v4/projects/$project_id/registry/repositories/$repo_id/tags/$tag_name"
      fi
    done
  done
done

# Run garbage collection after deletion
echo "Running garbage collection..."
sudo gitlab-ctl registry-garbage-collect -m