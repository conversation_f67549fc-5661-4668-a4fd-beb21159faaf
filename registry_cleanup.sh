#!/bin/bash

GITLAB_URL="https://gitlab.imtins.com"
PRIVATE_TOKEN="**************************"
CUTOFF_DATE=$(date -v-2y +%s)

# Function to calculate age in human readable format
calculate_age() {
  local created_timestamp=$1
  local current_timestamp=$(date +%s)
  local age_seconds=$((current_timestamp - created_timestamp))
  local age_days=$((age_seconds / 86400))
  local age_years=$((age_days / 365))
  local remaining_days=$((age_days % 365))

  if [ $age_years -gt 0 ]; then
    echo "${age_years} year(s) and ${remaining_days} day(s)"
  else
    echo "${age_days} day(s)"
  fi
}



# Get all projects with container registry enabled
projects_json=$(curl -s --header "PRIVATE-TOKEN: $PRIVATE_TOKEN" \
  "$GITLAB_URL/api/v4/projects?per_page=100&container_registry_enabled=true")

echo "$projects_json" | jq -c '.[]' | while read project; do
  project_id=$(echo "$project" | jq -r '.id')
  project_name=$(echo "$project" | jq -r '.name')
  echo "Processing project: $project_name (ID: $project_id)"
  
  # Get all repositories in the project
  repositories=$(curl -s --header "PRIVATE-TOKEN: $PRIVATE_TOKEN" \
    "$GITLAB_URL/api/v4/projects/$project_id/registry/repositories" | jq -r '.[].id')
  
  for repo_id in $repositories; do
    # Get all tags for this repository
    tags=$(curl -s --header "PRIVATE-TOKEN: $PRIVATE_TOKEN" \
      "$GITLAB_URL/api/v4/projects/$project_id/registry/repositories/$repo_id/tags?per_page=100")
    
    # Parse and delete old tags
    echo "$tags" | jq -c '.[]' | while read tag; do
      created_at=$(echo "$tag" | jq -r '.created_at')
      tag_name=$(echo "$tag" | jq -r '.name')
      
      # Convert created_at to timestamp (macOS compatible)
      tag_timestamp=$(date -j -f "%Y-%m-%dT%H:%M:%S" "${created_at%.*}" +%s 2>/dev/null || date -j -f "%Y-%m-%dT%H:%M:%S.%fZ" "$created_at" +%s 2>/dev/null || echo "0")
      
      if [ "$tag_timestamp" -lt "$CUTOFF_DATE" ]; then
        age=$(calculate_age "$tag_timestamp")
        echo "  Deleting old tag: $tag_name (age: $age, created: $created_at)"
        # curl -s --request DELETE --header "PRIVATE-TOKEN: $PRIVATE_TOKEN" \
        #   "$GITLAB_URL/api/v4/projects/$project_id/registry/repositories/$repo_id/tags/$tag_name"
      fi
    done
  done
done

echo "Done"
# Run garbage collection after deletion
# echo "Running garbage collection..."
# sudo gitlab-ctl registry-garbage-collect -m