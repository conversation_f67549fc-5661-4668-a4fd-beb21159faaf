#!/bin/bash

GITLAB_URL="https://gitlab.imtins.com"
PRIVATE_TOKEN="**************************"
CUTOFF_DATE=$(date -v-2y +%s)

# Test API connection first
echo "Testing GitLab API connection..."
test_response=$(curl -s -w "%{http_code}" --header "PRIVATE-TOKEN: $PRIVATE_TOKEN" \
  "$GITLAB_URL/api/v4/user" -o /tmp/gitlab_test.json)

if [ "$test_response" != "200" ]; then
  echo "Error: Cannot connect to GitLab API (HTTP $test_response)"
  echo "Response:"
  cat /tmp/gitlab_test.json
  exit 1
fi
echo "API connection successful!"

# Function to calculate age in human readable format
calculate_age() {
  local created_timestamp=$1
  local current_timestamp=$(date +%s)
  local age_seconds=$((current_timestamp - created_timestamp))
  local age_days=$((age_seconds / 86400))
  local age_years=$((age_days / 365))
  local remaining_days=$((age_days % 365))

  if [ $age_years -gt 0 ]; then
    echo "${age_years} year(s) and ${remaining_days} day(s)"
  else
    echo "${age_days} day(s)"
  fi
}



# Get all projects with container registry enabled
echo "Fetching projects from GitLab..."
curl -s --header "PRIVATE-TOKEN: $PRIVATE_TOKEN" \
  "$GITLAB_URL/api/v4/projects?per_page=100&container_registry_enabled=true" > /tmp/projects.json

echo "Response saved to /tmp/projects.json"

# Debug: Check if we got valid JSON by testing with a simple query
project_count=$(jq -r 'length' /tmp/projects.json 2>/dev/null)
if [ $? -ne 0 ] || [ -z "$project_count" ]; then
  echo "Error: Invalid JSON response from GitLab API"
  echo "First 500 characters of response:"
  head -c 500 /tmp/projects.json
  echo ""
  echo "Please check your GitLab URL and token"
  exit 1
fi

echo "JSON is valid. Number of projects found: $project_count"

# Extract only the fields we need to avoid JSON parsing issues with HTML content
jq -r '.[] | "\(.id)|\(.name)"' /tmp/projects.json | while IFS='|' read -r project_id project_name; do
  echo "Processing project: $project_name (ID: $project_id)"
  
  # Get all repositories in the project
  repositories=$(curl -s --header "PRIVATE-TOKEN: $PRIVATE_TOKEN" \
    "$GITLAB_URL/api/v4/projects/$project_id/registry/repositories" | jq -r '.[].id')
  
  for repo_id in $repositories; do
    # Get all tags for this repository
    tags=$(curl -s --header "PRIVATE-TOKEN: $PRIVATE_TOKEN" \
      "$GITLAB_URL/api/v4/projects/$project_id/registry/repositories/$repo_id/tags?per_page=100")

    # Parse and delete old tags
    if echo "$tags" | jq empty 2>/dev/null; then
      echo "$tags" | jq -r '.[].name' | while read tag_name; do
        # Get detailed information for each tag to get creation date
        tag_details=$(curl -s --header "PRIVATE-TOKEN: $PRIVATE_TOKEN" \
          "$GITLAB_URL/api/v4/projects/$project_id/registry/repositories/$repo_id/tags/$tag_name")

        created_at=$(echo "$tag_details" | jq -r '.created_at' 2>/dev/null || echo "null")
      
      # Convert created_at to timestamp (macOS compatible)
      if [ "$created_at" = "null" ] || [ -z "$created_at" ]; then
        tag_timestamp=0
      else
        # Handle different ISO 8601 formats with timezone
        # First try: 2025-08-11T03:27:39.136+00:00
        tag_timestamp=$(date -j -f "%Y-%m-%dT%H:%M:%S" "${created_at%%.*}" +%s 2>/dev/null)
        if [ $? -ne 0 ]; then
          # Second try: remove timezone and milliseconds
          clean_date=$(echo "$created_at" | sed 's/\.[0-9]*+.*$//' | sed 's/\.[0-9]*Z$//')
          tag_timestamp=$(date -j -f "%Y-%m-%dT%H:%M:%S" "$clean_date" +%s 2>/dev/null || echo "0")
        fi
      fi
      
      if [ "$tag_timestamp" -lt "$CUTOFF_DATE" ]; then
        if [ "$created_at" = "null" ] || [ -z "$created_at" ]; then
          echo "  Deleting old tag: $tag_name (age: unknown - no creation date, created: $created_at)"
        else
          age=$(calculate_age "$tag_timestamp")
          echo "  Deleting old tag: $tag_name (age: $age, created: $created_at)"
        fi
        # curl -s --request DELETE --header "PRIVATE-TOKEN: $PRIVATE_TOKEN" \
        #   "$GITLAB_URL/api/v4/projects/$project_id/registry/repositories/$repo_id/tags/$tag_name"
      else
        if [ "$created_at" != "null" ] && [ -n "$created_at" ]; then
          age=$(calculate_age "$tag_timestamp")
          echo "  Keeping recent tag: $tag_name (age: $age, created: $created_at)"
        fi
      fi
      done
    else
      echo "  Warning: Invalid JSON response for tags in repository $repo_id"
    fi
  done
done

echo "Done"
# Run garbage collection after deletion
# echo "Running garbage collection..."
# sudo gitlab-ctl registry-garbage-collect -m